/*
 * copyright (c) 2025 LayerNext, Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Handle the request related to the quickbooks api
 */
/**
 * @class QuickbooksApiService
 * @description This QuickbooksApiService use for Handle the request related to the quickbooks api
 * <AUTHOR>
 */

import {BindingKey, BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {logger} from '../config';
import {AuthStatus} from '../models';
import {DataSourceAuthorizationRepository} from '../repositories';
const OAuthClient = require('intuit-oauth');
const QuickBooksClient = require('node-quickbooks');

@injectable({scope: BindingScope.TRANSIENT})
export class QuickBooksApiService {
  private clientId: string;
  private clientSecret: string;
  // Map of teamId to QB client
  private qb_clients: Map<string, typeof QuickBooksClient> = new Map();
  private isQBClientsInitialized: boolean = false;

  constructor(
    @repository(DataSourceAuthorizationRepository)
    private dataSourceAuthRepository: DataSourceAuthorizationRepository,
  ) {
    this.clientId = process.env.QB_CLIENT_ID || '';
    this.clientSecret = process.env.QB_CLIENT_SECRET || '';
    this.initializeQBClients();
  }

  async getOAuthClient(): Promise<typeof OAuthClient> {
    // Load the keys from .env
    const redirectUri = process.env.QB_REDIRECT_URI || '';
    const environment = process.env.QB_ENV || '';
    if (!this.clientId || !this.clientSecret || !redirectUri || !environment) {
      throw new HttpErrors.Unauthorized('Quickbooks credentials not found');
    }
    const qbOauthClient = new OAuthClient({
      clientId: this.clientId,
      clientSecret: this.clientSecret,
      environment,
      redirectUri,
    });
    return qbOauthClient;
  }

  /**
   * Initialize QB clients from all active DataSourceAuthentication entries
   */
  private async initializeQBClients(): Promise<void> {
    const is_prod = process.env.QB_ENV === 'production';
    try {
      const activeAuths = await this.dataSourceAuthRepository.find({
        where: {
          sourceType: 'QuickBooks',
          authStatus: AuthStatus.ACTIVE,
        },
      });

      for (const auth of activeAuths) {
        if (auth.authTokens?.accessToken && auth.companyId) {
          const qbClient = new QuickBooksClient(
            this.clientId,
            this.clientSecret,
            auth.authTokens.accessToken,
            false,
            auth.companyId,
            !is_prod,
            true,
            null,
            '2.0',
            auth.authTokens.refreshToken,
          );

          this.qb_clients.set(auth.teamId.toString(), qbClient);
          logger.info(`QuickBooksApiService | QB client initialized | companyId: ${auth.companyId}`);
        }
      }
      this.isQBClientsInitialized = true;
      logger.info(`QuickBooksApiService | Initialized ${this.qb_clients.size} QB clients`);
    } catch (error) {
      logger.error(`QuickBooksApiService | Failed to initialize QB clients | ${error}`);
    }
  }
  async getAuthUri(teamId: string) {
    try {
      const scope = process.env.QB_SCOPES || OAuthClient.scopes.Accounting;
      const qb_oauthClient = await this.getOAuthClient();
      const authUri = qb_oauthClient.authorizeUri({
        scope: [scope],
        state: `csrf-${Math.random().toString(36).slice(2)}-${teamId}`, //Set teamId in state to retrieve after auth
      });

      // Create pending auth record
      await this.dataSourceAuthRepository.create({
        teamId,
        sourceType: 'QuickBooks',
        companyId: 'pending',
        authStatus: AuthStatus.PENDING,
      });

      logger.info(`QuickBooksApiService | getAuthUri success | authUri: ${authUri}`);
      return authUri;
    } catch (error) {
      logger.error(`QuickBooksApiService | getAuthUri failed | ${error}`);
      throw new HttpErrors.Unauthorized('Quickbooks connection failed');
    }
  }

  async onOAuthRedirect(redirectUrl: string) {
    try {
      const qb_oauthClient = await this.getOAuthClient();
      const tokenResponse = await qb_oauthClient.createToken(redirectUrl);
      const accessToken = tokenResponse.getJson().access_token;
      const refreshToken = tokenResponse.getJson().refresh_token;
      const realmId = tokenResponse.token.realmId;
      const is_prod = process.env.QB_ENV === 'production';
      // Get team id from state
      const teamId = tokenResponse.token.state.split('-')[2];
      // Find the pending auth record for company id
      const existingAuth = await this.dataSourceAuthRepository.findOne({
        where: {
          sourceType: 'QuickBooks',
          teamId: teamId,
        },
      });
      if (!existingAuth) {
        throw new HttpErrors.Unauthorized('Quickbooks company not found');
      }

      // Create QB client for this company
      const qbClient = new QuickBooksClient(
        this.clientId,
        this.clientSecret,
        accessToken,
        false,
        realmId,
        !is_prod,
        true,
        null,
        '2.0',
        refreshToken,
      );

      // Store client in map
      this.qb_clients.set(existingAuth.teamId.toString(), qbClient);

      logger.info(`QuickBooksApiService | QuickBooks client created | realmId: ${realmId}`);

      // Test connection
      const accounts = await qbClient.findAccounts();
      console.log(accounts);

      // Update the auth record
      await this.dataSourceAuthRepository.updateById(existingAuth._id, {
        companyId: realmId,
        authTokens: {
          accessToken,
          refreshToken,
        },
        authStatus: AuthStatus.ACTIVE,
        updatedAt: new Date(),
      });

      return {
        accessToken,
        refreshToken,
        realmId,
      };
    } catch (error) {
      logger.error(`QuickBooksApiService | onOAuthRedirect failed | ${error}`);
      throw new HttpErrors.Unauthorized('Quickbooks token creation failed');
    }
  }

  /**
   * Refresh the access token for a specific team
   */
  async refreshAccessToken(teamId: string) {
    try {
      const is_prod = process.env.QB_ENV === 'production';
      const qb_oauthClient = await this.getOAuthClient();
      const existingAuth = await this.dataSourceAuthRepository.findOne({
        where: {
          sourceType: 'QuickBooks',
          teamId: teamId,
        },
      });
      if (!existingAuth) {
        throw new HttpErrors.Unauthorized('Quickbooks company not found');
      }
      const refreshToken = existingAuth.authTokens?.refreshToken;
      if (!refreshToken) {
        throw new HttpErrors.Unauthorized('Quickbooks refresh token not found');
      }
      const tokenResponse = await qb_oauthClient.refreshUsingToken(refreshToken);
      const accessToken = tokenResponse.getJson().access_token;
      const newRefreshToken = tokenResponse.getJson().refresh_token;
      // Update the auth record
      await this.dataSourceAuthRepository.updateById(existingAuth._id, {
        authTokens: {
          accessToken,
          refreshToken: newRefreshToken,
        },
        updatedAt: new Date(),
      });
      // Refresh the QB API client for the company (Remove the previous one and create a new one)
      this.qb_clients.delete(teamId);
      const qbClient = new QuickBooksClient(
        this.clientId,
        this.clientSecret,
        accessToken,
        false,
        existingAuth.companyId,
        !is_prod,
        true,
        null,
        '2.0',
        newRefreshToken,
      );
      this.qb_clients.set(teamId, qbClient);
      return accessToken;
    } catch (error) {
      logger.error(`QuickBooksApiService | refreshQBToken failed | ${error}`);
      return null;
    }
  }

  /**
   * Get QB client for a specific team
   */
  async getQBClientForCompany(teamId: string): Promise<typeof QuickBooksClient | undefined> {
    if (!this.isQBClientsInitialized) {
      await this.initializeQBClients();
    }
    return this.qb_clients.get(teamId);
  }

  /**
   * Call QuickBooks APIs for a specific company
   */
  async callQBApi(
    teamId: string,
    apiName: string,
    apiParams: any,
    isAuthRefreshRequired: boolean = false,
  ): Promise<any> {
    logger.info(`QuickBooksApiService | callQBApi | teamId: ${teamId}, apiName: ${apiName}, apiParams: ${apiParams}`);
    if (isAuthRefreshRequired) {
      const newAccessToken = await this.refreshAccessToken(teamId);
      if (!newAccessToken) {
        throw new HttpErrors.Unauthorized('Quickbooks token refresh failed');
      }
    }
    const qbClient: typeof QuickBooksClient = await this.getQBClientForCompany(teamId);
    if (!qbClient) {
      throw new HttpErrors.Unauthorized('Quickbooks client for company not found');
    }

    try {
      if (
        [
          'createVendor',
          'updateVendor',
          'createBill',
          'updateBill',
          'createBillPayment',
          'updateBillPayment',
          'createPurchase',
          'updatePurchase',
        ].includes(apiName)
      ) {
        return await this.executeQBMethod(qbClient, apiName, apiParams, teamId, apiName);
      } else {
        throw new HttpErrors.NotFound('Quickbooks api ' + apiName + ' not found');
      }
    } catch (error) {
      logger.error(`QuickBooksApiService | callQBApi failed | ${error}`);
      throw new HttpErrors.BadRequest(`Quickbooks api call failed: ${error}`);
    }
  }

  async queryQBObject(
    teamId: string,
    objectType: string,
    query: any,
    isAuthRefreshRequired: boolean = false,
  ): Promise<any> {
    logger.info(`QuickBooksApiService | queryQBObject | teamId: ${teamId}, objectType: ${objectType}, query: ${query}`);
    if (isAuthRefreshRequired) {
      const newAccessToken = await this.refreshAccessToken(teamId);
      if (!newAccessToken) {
        throw new HttpErrors.Unauthorized('Quickbooks token refresh failed');
      }
    }
    const qbClient: typeof QuickBooksClient = await this.getQBClientForCompany(teamId);
    if (!qbClient) {
      throw new HttpErrors.Unauthorized('Quickbooks client for company not found');
    }
    try {
      switch (objectType) {
        case 'Vendor':
          return await this.executeQBMethod(
            qbClient,
            'findVendors',
            {objectType: 'Vendor', query: query},
            teamId,
            'queryQBObject',
          );
        case 'Account':
          return await this.executeQBMethod(
            qbClient,
            'findAccounts',
            {objectType: 'Account', query: query},
            teamId,
            'queryQBObject',
          );
        case 'TaxCode':
          return await this.executeQBMethod(
            qbClient,
            'findTaxCodes',
            {objectType: 'TaxCode', query: query},
            teamId,
            'queryQBObject',
          );
        case 'PaymentMethod':
          return await this.executeQBMethod(
            qbClient,
            'findPaymentMethods',
            {objectType: 'PaymentMethod', query: query},
            teamId,
            'queryQBObject',
          );
        case 'Purchase':
          return await this.executeQBMethod(
            qbClient,
            'findPurchases',
            {objectType: 'Purchase', query: query},
            teamId,
            'queryQBObject',
          );
        case 'Bill':
          return await this.executeQBMethod(
            qbClient,
            'findBills',
            {objectType: 'Bill', query: query},
            teamId,
            'queryQBObject',
          );
        case 'BillPayment':
          return await this.executeQBMethod(
            qbClient,
            'findBillPayments',
            {objectType: 'BillPayment', query: query},
            teamId,
            'queryQBObject',
          );
        case 'Term':
          return await this.executeQBMethod(
            qbClient,
            'findTerms',
            {objectType: 'Term', query: query},
            teamId,
            'queryQBObject',
          );
        default:
          return {
            error: 'QuickBooks object type ' + objectType + ' not found',
            isSuccess: false,
          };
      }
    } catch (error) {
      logger.error(`QuickBooksApiService | queryQBObject failed | ${error}`);
      return this.onApiError(error, teamId, 'queryQBObject', {
        objectType,
        query,
      });
    }
  }

  async getQBObjectById(
    teamId: string,
    objectType: string,
    id?: string,
    isAuthRefreshRequired: boolean = false,
  ): Promise<any> {
    logger.info(`QuickBooksApiService | getQBObjectById | teamId: ${teamId}, objectType: ${objectType}, id: ${id}`);
    if (isAuthRefreshRequired) {
      const newAccessToken = await this.refreshAccessToken(teamId);
      if (!newAccessToken) {
        throw new HttpErrors.Unauthorized('Quickbooks token refresh failed');
      }
    }
    const qbClient: typeof QuickBooksClient = await this.getQBClientForCompany(teamId);
    if (!qbClient) {
      throw new HttpErrors.Unauthorized('Quickbooks client for company not found');
    }
    try {
      switch (objectType) {
        case 'CompanyInfo':
          // Set the id from the client in case of CompanyInfo
          id = qbClient.realmId;
          return await this.executeQBMethod(
            qbClient,
            'getCompanyInfo',
            {objectType: 'CompanyInfo', id: id},
            teamId,
            'getQBObjectById',
          );
        case 'Preferences':
          // No id required for Preferences
          return await this.executeQBMethod(
            qbClient,
            'getPreferences',
            {objectType: 'Preferences', id: null},
            teamId,
            'getQBObjectById',
          );
        case 'Vendor':
          return await this.executeQBMethod(
            qbClient,
            'getVendor',
            {objectType: 'Vendor', id: id},
            teamId,
            'getQBObjectById',
          );
        case 'Account':
          return await this.executeQBMethod(
            qbClient,
            'getAccount',
            {objectType: 'Account', id: id},
            teamId,
            'getQBObjectById',
          );
      }
    } catch (error) {
      logger.error(`QuickBooksApiService | getQBObjectById failed | ${error}`);
      this.onApiError(error, teamId, 'getQBObjectById', {
        objectType,
        id,
      });
      return null;
    }
  }

  async uploadAttachment(
    teamId: string,
    fileStream: any,
    file_name: string,
    content_type: string,
    object_type: string,
    object_id: string,
    isAuthRefreshRequired: boolean = false,
  ): Promise<any> {
    logger.info(
      `QuickBooksApiService | uploadAttachment | teamId: ${teamId}, file_name: ${file_name}, content_type: ${content_type}, object_type: ${object_type}, object_id: ${object_id}`,
    );
    if (isAuthRefreshRequired) {
      const newAccessToken = await this.refreshAccessToken(teamId);
      if (!newAccessToken) {
        throw new HttpErrors.Unauthorized('Quickbooks token refresh failed');
      }
    }
    const qbClient: typeof QuickBooksClient = await this.getQBClientForCompany(teamId);
    if (!qbClient) {
      throw new HttpErrors.Unauthorized('Quickbooks client for company not found');
    }

    // Ensure fileStream is properly initialized and has the required properties
    if (!fileStream || typeof fileStream !== 'object') {
      throw new HttpErrors.BadRequest('Invalid file stream provided');
    }

    // Ensure the stream has proper event emitter capabilities
    if (typeof fileStream.on !== 'function' || typeof fileStream.emit !== 'function') {
      throw new HttpErrors.BadRequest('File stream must be a proper Node.js stream');
    }

    // Log stream properties for debugging
    logger.info(`QuickBooksApiService | uploadAttachment | Stream type: ${fileStream.constructor.name}`);
    logger.info(`QuickBooksApiService | uploadAttachment | Stream readable: ${fileStream.readable}`);
    logger.info(`QuickBooksApiService | Stream has on method: ${typeof fileStream.on === 'function'}`);
    logger.info(`QuickBooksApiService | Stream has emit method: ${typeof fileStream.emit === 'function'}`);

    try {
      return await new Promise((resolve, reject) => {
        // Add error handling for the stream
        fileStream.on('error', (streamError: any) => {
          logger.error(`QuickBooksApiService | uploadAttachment stream error | ${streamError}`);
          reject(new HttpErrors.InternalServerError('File stream error occurred'));
        });

        qbClient.upload(file_name, content_type, fileStream, object_type, object_id, (err: any, result: any) => {
          if (err) {
            logger.error(`QuickBooksApiService | uploadAttachment QB error | ${JSON.stringify(err)}`);
            this.onApiError(err, teamId, 'uploadAttachment', {
              fileStream,
              file_name,
              content_type,
              object_type,
              object_id,
            })
              .then(resolve)
              .catch(reject);
          } else {
            // For upload operations, return both Id and SyncToken if available
            if (result.Id && result.SyncToken !== undefined) {
              resolve({
                Id: result.Id,
                SyncToken: result.SyncToken,
              });
            } else {
              resolve(result.Id);
            }
          }
        });
      });
    } catch (error) {
      logger.error(`QuickBooksApiService | uploadAttachment failed | ${error}`);
      throw new HttpErrors.InternalServerError('Quickbooks api call failed');
    }
  }

  async onApiError(error: any, teamId: string, apiName: string, apiParams: any) {
    logger.error(`QuickBooksApiService | callQBApi failed | ${error}`);
    if (error.fault && error.fault.type == 'AUTHENTICATION') {
      if (apiName == 'uploadAttachment') {
        return this.uploadAttachment(
          teamId,
          apiParams.fileStream,
          apiParams.file_name,
          apiParams.content_type,
          apiParams.object_type,
          apiParams.object_id,
          true,
        );
      } else if (apiName == 'queryQBObject') {
        return this.queryQBObject(teamId, apiParams.objectType, apiParams.query, true);
      } else if (apiName == 'getQBObjectById') {
        return this.getQBObjectById(teamId, apiParams.objectType, apiParams.id, true);
      } else {
        return this.callQBApi(teamId, apiName, apiParams, true);
      }
    }
    let errMsgString = JSON.stringify(error);
    if (error.Fault?.Error?.[0] && error.Fault.Error[0]) {
      errMsgString = error.Fault.Error[0].Message + ': ' + error.Fault.Error[0].Detail;
    }
    return {
      error: errMsgString,
      isSuccess: false,
    };
    //throw new HttpErrors.InternalServerError(errMsgString);
  }

  private executeQBMethod(
    qbClient: any,
    methodName: string,
    apiParams: any,
    teamId: string,
    apiName: string,
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      // Ensure apiParams is a proper JavaScript object, not a JSON string
      let cleanedParams;

      if (apiName == 'queryQBObject') {
        if (typeof apiParams.query === 'object') {
          // Add fetchAll to get all records at once
          cleanedParams = {...apiParams.query, fetchAll: true};
        } else {
          cleanedParams = apiParams.query;
        }
      } else if (apiName == 'getQBObjectById') {
        cleanedParams = apiParams.id;
      } else if (typeof apiParams === 'string') {
        cleanedParams = JSON.parse(apiParams);
      } else {
        cleanedParams = JSON.parse(JSON.stringify(apiParams));
      }

      const method = qbClient[methodName].bind(qbClient);

      if (cleanedParams == null) {
        method((err: any, result: any) => {
          if (err) {
            logger.error(`QuickBooksApiService | ${methodName} error: ${JSON.stringify(err)}`);
            this.onApiError(err, teamId, apiName, apiParams).then(resolve).catch(reject);
          } else {
            logger.debug(`QuickBooksApiService | ${methodName} success: ${JSON.stringify(result)}`);
            resolve(result);
          }
        });
      } else {
        method(cleanedParams, (err: any, result: any) => {
          if (err) {
            logger.error(`QuickBooksApiService | ${methodName} error: ${JSON.stringify(err)}`);
            this.onApiError(err, teamId, apiName, apiParams).then(resolve).catch(reject);
          } else {
            logger.debug(`QuickBooksApiService | ${methodName} success: ${JSON.stringify(result)}`);
            // If both Id and SyncToken are available, return them together - for all update/create Apis (excluding uploadAttachment, queryQBObject, getQBObjectById)
            if (
              apiName != 'uploadAttachment' &&
              apiName != 'queryQBObject' &&
              apiName != 'getQBObjectById' &&
              result.Id &&
              result.SyncToken !== undefined
            ) {
              resolve({
                Id: result.Id,
                SyncToken: result.SyncToken,
              });
            } else {
              resolve(result);
            }
          }
        });
      }
    });
  }
}

export const QUICKBOOKS_API_SERVICE = BindingKey.create<QuickBooksApiService>('service.quickBooksApiService');

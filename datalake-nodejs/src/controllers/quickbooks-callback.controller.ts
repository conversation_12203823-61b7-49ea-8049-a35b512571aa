/*
 * Copyright (c) 2025 LayerNext, Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
 * Handle the request related to the Quickbooks - Authentication callbacks and API calls
 */
import {inject} from '@loopback/core';
import {get, HttpErrors, param, post, Request, requestBody, RestBindings} from '@loopback/rest';
import multer = require('multer');
import {Readable} from 'stream';
import {logger} from '../config';
import {QUICKBOOKS_API_SERVICE, QuickBooksApiService} from '../services/quickbooks-api-service';
import {DatalakeUserMessages} from '../settings/datalake.user.messages';
import {BASIC_AUTH_SERVICE, BasicAuthService} from '../authServices/basic-auth.service';
import * as fs from 'fs';

/**
 * @class QuickBooksCallbackController
 * Handle the request related to the Quickbooks - Authentication callbacks and API calls
 * @description This is a controller class dedicated for Quickbooks integration
 * <AUTHOR>
 */
export class QuickBooksCallbackController {
  constructor(
    @inject(BASIC_AUTH_SERVICE) private basicAuthService: BasicAuthService,
    @inject(QUICKBOOKS_API_SERVICE) private quickBooksApiService: QuickBooksApiService,
  ) {}

  /**
   * Webhook to handle the quickbooks oauth redirect - called by quickbooks after user authorization
   * @param request Request with oauth redirect url
   * @returns Tokens object
   */
  @get('/api/quickbooks/oauth_callback')
  async handleQuickBooksCallback(@inject(RestBindings.Http.REQUEST) request: Request) {
    try {
      const tokensObj = await this.quickBooksApiService.onOAuthRedirect(request.url);
      return tokensObj;
    } catch (error) {
      logger.error(`QuickBooksCallbackController | handleQuickBooksCallback failed | ${error}`);
      throw new HttpErrors.Unauthorized('Quickbooks callback failed');
    }
  }

  /**
   * Creates a pending authorization record and returns the authorization url for the frontend to redirect user
   * @param authorization Basic auth header
   * @returns Object containing pending authorization url
   */
  @get('/api/quickbooks/get_auth_uri')
  async getQuickBooksAuthUri(@param.header.string('Authorization') authorization: string) {
    // First authenticate the user
    //const authorizeDetails = await this.basicAuthService.basicAuthentication(authorization);
    const teamId = "6374c3decb468b7a7a68a116";// authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Get dictionary data | DatalakeClientInterfaceController.getDataDictionary | N/A | Couldn't find the team `
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    try {
      const authUri = await this.quickBooksApiService.getAuthUri(teamId);
      return {
        "authUri": authUri,
      };
    } catch (error) {
      logger.error(`QuickBooksCallbackController | acceptQuickBooksConnection failed | ${error}`);
      throw new HttpErrors.Unauthorized('Quickbooks connection failed');
    }
  }

  /**
   * Executes a QuickBooks API call which creates or updates an object (Eg: Vendor, Bill, BillPayment, Purchase, etc.)
   * @param authorization Basic auth header
   * @param requestBody Object containing apiName and apiParams for QuickBooks API call
   * @returns 
   */
  @post('/api/quickbooks/create_or_update_object')
  async createOrUpdateQBObject(
    @param.header.string('Authorization') authorization: string,
    @requestBody() requestBody: any,
  ) {
    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorization);
    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Get dictionary data | DatalakeClientInterfaceController.getDataDictionary | N/A | Couldn't find the team `
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    try {
      const result = await this.quickBooksApiService.callQBApi(teamId, requestBody.apiName, requestBody.apiParams);
      
      // Check if the result contains both Id and SyncToken (for create/update operations)
      if (result && result.isSuccess === false) {
        throw new HttpErrors.BadRequest(result.error);
      }
      else if (result && typeof result === 'object' && result.Id && result.SyncToken !== undefined) {
        return {
          "createdId": result.Id,
          "syncToken": result.SyncToken,
        };
      } else {
        // For other operations, return as before
        return {
          "createdId": result,
        };
      }
    } catch (error) {
      logger.error(`QuickBooksCallbackController | createOrUpdateQBObject failed | ${error}`);
      throw error;
    }
  }

  /**
   * Uploads an attachment to a QuickBooks object (Eg: Vendor, Bill, BillPayment, Purchase, etc.)
   * @param authorization Basic auth header
   * @param request Request with file and object details (Multipart form data)
   * @returns 
   */ 
  @post('/api/quickbooks/upload_attachment')
  async uploadAttachment(
    @param.header.string('Authorization') authorization: string,
    @requestBody({
      description: 'multipart/form-data file upload for QuickBooks attachment',
      required: true,
      content: {
        'multipart/form-data': {
          'x-parser': 'stream',
          schema: {
            type: 'object',
            properties: {
              file: {type: 'string', format: 'binary'},
              object_type: {type: 'string'},
              object_id: {type: 'string'}
            }
          }
        }
      }
    }) request: Request,
  ) {
    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorization);
    const teamId = authorizeDetails.teamId;
    logger.debug(`teamId: ${teamId}`);

    if (!teamId) {
      logger.error(
        `Get dictionary data | DatalakeClientInterfaceController.getDataDictionary | N/A | Couldn't find the team `
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    try {
      const storage = multer.memoryStorage();
      const upload = multer({storage});
      
      const formData = await new Promise<{fields: any, files: any}>((resolve, reject) => {
        upload.any()(request as any, {} as any, (err: any) => {
          if (err) reject(err);
          else {
            const fields: any = {};
            const files: any = {};
            
            (request as any).body = (request as any).body || {};
            Object.keys((request as any).body).forEach(key => {
              fields[key] = (request as any).body[key];
            });
            
            if ((request as any).files) {
              (request as any).files.forEach((file: any) => {
                files[file.fieldname] = file;
              });
            }
            
            resolve({fields, files});
          }
        });
      });

      const file = formData.files.file;
      if (!file) {
        throw new HttpErrors.BadRequest('No file uploaded');
      }
      
      const objectType = formData.fields.object_type || formData.fields.objectType;
      const objectId = formData.fields.object_id || formData.fields.objectId;
      
      if (!objectType || !objectId) {
        throw new HttpErrors.BadRequest('object_type and object_id are required');
      }
      //Get the mime type of the file using extension
      const fileExtension = file.originalname.split('.').pop().toLowerCase();
      let mimeType = file.mimetype;
      switch (fileExtension) {
        case 'pdf': 
          mimeType = 'application/pdf';
          break;
        case 'jpg': 
        case 'jpeg': 
          mimeType = 'image/jpeg';
          break;
        case 'png': 
          mimeType = 'image/png';
          break;
        default: 
          mimeType = 'application/octet-stream';
          break;
      }
      //Save the data to a temporary file in order to create a readable stream
      const tempFilePath = './temp/' + file.originalname;
      fs.writeFileSync(tempFilePath, file.buffer);
      logger.debug(`QuickBooksCallbackController | uploadAttachment | tempFilePath: ${tempFilePath}`);
      
      const fileStream = fs.createReadStream(tempFilePath);
      const result = await this.quickBooksApiService.uploadAttachment(
        teamId, 
        fileStream,
        file.originalname, 
        mimeType, 
        objectType, 
        objectId
      );
      
      //Cleanup the temporary files
      fs.unlinkSync(tempFilePath);

      // Check if the result contains both Id and SyncToken
      if (result && typeof result === 'object' && result.Id && result.SyncToken !== undefined) {
        return {
          "createdId": result.Id,
          "syncToken": result.SyncToken,
        };
      } else {
        // For uploads that only return Id
        return {
          "createdId": result,
        };
      }
    } catch (error) {
      logger.error(`QuickBooksCallbackController | uploadAttachment failed | ${error}`);
      throw new HttpErrors.InternalServerError('Quickbooks api call failed');
    }
  }

  /**
   * Retrieves QuickBooks entity instances for a given query
   * @param authorization Basic auth header
   * @param requestBody Object containing objectType and query for QuickBooks API
   * @returns
   */
  @post('/api/quickbooks/query_object')
  async queryQBObject(
    @param.header.string('Authorization') authorization: string,
    @requestBody() requestBody: any,
  ) {
    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorization);
    const teamId = authorizeDetails.teamId; // "6374c3decb468b7a7a68a116";

    if (!teamId) {
      logger.error(
        `Get dictionary data | DatalakeClientInterfaceController.getDataDictionary | N/A | Couldn't find the team `
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }

    // Validate request body
    if (!requestBody || !requestBody.objectType) {
      throw new HttpErrors.BadRequest('objectType is required');
    }

    // Validate and format query parameter
    let formattedQuery = requestBody.query;

    // If query is an array (list of dictionaries), reject it as QuickBooks API doesn't support this format
    if (Array.isArray(requestBody.query)) {
      throw new HttpErrors.BadRequest(
        'Query parameter cannot be an array. QuickBooks API expects a single object with field-value pairs or a query string. ' +
        'Example: {"Name": "John Doe"} or {"Active": true, "limit": 10}'
      );
    }

    // If query is a string that looks like JSON, try to parse it
    if (typeof requestBody.query === 'string') {
      try {
        // Check if it's a JSON string
        if (requestBody.query.trim().startsWith('{') || requestBody.query.trim().startsWith('[')) {
          const parsedQuery = JSON.parse(requestBody.query);
          if (Array.isArray(parsedQuery)) {
            throw new HttpErrors.BadRequest(
              'Query parameter cannot be an array. QuickBooks API expects a single object with field-value pairs.'
            );
          }
          formattedQuery = parsedQuery;
        }
        // If it's a plain string, leave it as is (it might be a SQL-like query string)
      } catch (parseError) {
        throw new HttpErrors.BadRequest(`Invalid JSON in query parameter: ${parseError.message}`);
      }
    }

    try {
      const result = await this.quickBooksApiService.queryQBObject(teamId, requestBody.objectType, formattedQuery);
      if (result && result.isSuccess === false) {
        throw new HttpErrors.BadRequest(result.error);
      }
      return result;
    } catch (error) {
      logger.error(`QuickBooksCallbackController | queryQBObject failed | ${error}`);
      // If this is HTTP error, throw it as is
      if (error instanceof HttpErrors.HttpError) {
        throw error;
      }
      throw new HttpErrors.InternalServerError('Quickbooks query failed');
    }
  }

  /**
   * Retrieves QuickBooks entity instance for a given id
   * @param authorization Basic auth header
   * @param objectType Type of QuickBooks object
   * @param objectId Id of QuickBooks object
   * @returns 
   */
  @get('/api/quickbooks/get_object_by_id')
  async getQBObjectById(
    @param.header.string('Authorization') authorization: string,
    @param.query.string('objectType') objectType: string,
    @param.query.string('objectId') objectId?: string,
  ) {
    const authorizeDetails = await this.basicAuthService.basicAuthentication(authorization);
    const teamId = authorizeDetails.teamId;

    if (!teamId) {
      logger.error(
        `Get dictionary data | DatalakeClientInterfaceController.getDataDictionary | N/A | Couldn't find the team `
      );
      throw new HttpErrors.NotAcceptable(DatalakeUserMessages.LAYERNEXT_TEAM_NOT_EXIST);
    }
    try {
      const result = await this.quickBooksApiService.getQBObjectById(teamId, objectType, objectId);
      if (result && result.isSuccess === false) {
        throw new HttpErrors.BadRequest(result.error);
      }
      return result;
    } catch (error) {
      logger.error(`QuickBooksCallbackController | getQBObjectById failed | ${error}`);
      // If this is HTTP error, throw it as is
      if (error instanceof HttpErrors.HttpError) {
        throw error;
      }
      throw new HttpErrors.InternalServerError('Quickbooks get object failed');
    }
  }
}

/**
 * Test script to validate the query_object endpoint behavior
 * This script demonstrates the correct and incorrect ways to call the endpoint
 */

const axios = require('axios');

// Base URL for your API
const BASE_URL = 'http://localhost:3000'; // Adjust as needed
const ENDPOINT = '/api/quickbooks/query_object';

// Mock authorization header (replace with actual auth)
const AUTH_HEADER = 'Basic your-auth-token-here';

async function testQueryObject() {
  console.log('Testing QuickBooks query_object endpoint...\n');

  // Test 1: Valid query with object
  console.log('Test 1: Valid query with object');
  try {
    const validRequest = {
      objectType: 'Vendor',
      query: {
        Active: true,
        limit: 10
      }
    };
    
    console.log('Request:', JSON.stringify(validRequest, null, 2));
    // Uncomment to actually make the request:
    // const response = await axios.post(`${BASE_URL}${ENDPOINT}`, validRequest, {
    //   headers: { 'Authorization': AUTH_HEADER }
    // });
    // console.log('Response:', response.data);
    console.log('✅ This format should work\n');
  } catch (error) {
    console.log('❌ Error:', error.response?.data || error.message);
  }

  // Test 2: Invalid query with array (this should fail)
  console.log('Test 2: Invalid query with array (should fail)');
  try {
    const invalidRequest = {
      objectType: 'Vendor',
      query: [
        { Name: 'John Doe' },
        { Name: 'Jane Smith' }
      ]
    };
    
    console.log('Request:', JSON.stringify(invalidRequest, null, 2));
    // Uncomment to actually make the request:
    // const response = await axios.post(`${BASE_URL}${ENDPOINT}`, invalidRequest, {
    //   headers: { 'Authorization': AUTH_HEADER }
    // });
    console.log('❌ This format should be rejected with a clear error message\n');
  } catch (error) {
    console.log('✅ Expected error:', error.response?.data || error.message);
  }

  // Test 3: Valid query with string
  console.log('Test 3: Valid query with string');
  try {
    const stringRequest = {
      objectType: 'Vendor',
      query: "Active = true"
    };
    
    console.log('Request:', JSON.stringify(stringRequest, null, 2));
    console.log('✅ This format should work\n');
  } catch (error) {
    console.log('❌ Error:', error.response?.data || error.message);
  }

  // Test 4: Invalid JSON string query
  console.log('Test 4: Invalid JSON string query (should fail)');
  try {
    const invalidJsonRequest = {
      objectType: 'Vendor',
      query: '{"Name": "John Doe", "Active": true' // Missing closing brace
    };
    
    console.log('Request:', JSON.stringify(invalidJsonRequest, null, 2));
    console.log('❌ This format should be rejected with a JSON parse error\n');
  } catch (error) {
    console.log('✅ Expected error:', error.response?.data || error.message);
  }
}

// Example of correct usage patterns
console.log('=== CORRECT USAGE EXAMPLES ===\n');

console.log('1. Simple field filter:');
console.log(JSON.stringify({
  objectType: 'Vendor',
  query: { Active: true }
}, null, 2));

console.log('\n2. Multiple field filters:');
console.log(JSON.stringify({
  objectType: 'Vendor',
  query: { 
    Active: true,
    Name: 'John Doe'
  }
}, null, 2));

console.log('\n3. With pagination:');
console.log(JSON.stringify({
  objectType: 'Vendor',
  query: { 
    Active: true,
    limit: 50,
    offset: 0
  }
}, null, 2));

console.log('\n4. SQL-like string query:');
console.log(JSON.stringify({
  objectType: 'Vendor',
  query: "Active = true AND Name LIKE '%John%'"
}, null, 2));

console.log('\n=== INCORRECT USAGE (WILL BE REJECTED) ===\n');

console.log('❌ Array of objects:');
console.log(JSON.stringify({
  objectType: 'Vendor',
  query: [
    { Name: 'John' },
    { Name: 'Jane' }
  ]
}, null, 2));

console.log('\n❌ Nested arrays:');
console.log(JSON.stringify({
  objectType: 'Vendor',
  query: {
    Names: ['John', 'Jane'] // This might work depending on QB API support
  }
}, null, 2));

// Run the tests (commented out to avoid actual API calls)
// testQueryObject();
